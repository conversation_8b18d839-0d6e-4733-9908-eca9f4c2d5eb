# 验证码功能测试数据设置指南

## 概述

为了完整测试新的验证码验证功能，需要在数据库中手动插入一些测试数据。本文档提供了不同数据库类型的测试数据插入方法。

## 测试场景

新的验证码验证功能包含以下测试场景：

1. **首次验证有效验证码**：验证码存在但没有 ca_code
2. **验证码已使用**：验证码存在且已有 ca_code
3. **验证码不存在**：数据库中没有对应记录
4. **ca_code 验证**：验证 code 和 ca_code 的匹配性

## 数据库测试数据

### MongoDB 测试数据

```javascript
// 连接到 MongoDB
use your_database_name;

// 插入测试数据到 ca_code_list 集合
db.ca_code_list.insertMany([
  {
    app_name: "test_app",
    code: "valid_code_123",
    ca_code: "",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    app_name: "test_app", 
    code: "used_code_456",
    ca_code: "5d41402abc4b2a76b9719d911017c592",
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    app_name: "test_workflow",
    code: "workflow_test_123", 
    ca_code: "",
    created_at: new Date(),
    updated_at: new Date()
  }
]);
```

### SQL Server 测试数据

```sql
-- 插入测试数据到 ca_code_list 表
INSERT INTO ca_code_list (app_name, code, ca_code, created_at, updated_at) VALUES
('test_app', 'valid_code_123', '', GETDATE(), GETDATE()),
('test_app', 'used_code_456', '5d41402abc4b2a76b9719d911017c592', GETDATE(), GETDATE()),
('test_workflow', 'workflow_test_123', '', GETDATE(), GETDATE());
```

### SQLite 测试数据

```sql
-- 插入测试数据到 ca_code_list 表
INSERT INTO ca_code_list (app_name, code, ca_code, created_at, updated_at) VALUES
('test_app', 'valid_code_123', '', datetime('now'), datetime('now')),
('test_app', 'used_code_456', '5d41402abc4b2a76b9719d911017c592', datetime('now'), datetime('now')),
('test_workflow', 'workflow_test_123', '', datetime('now'), datetime('now'));
```

## 测试步骤

1. **启动服务器**
   ```bash
   ./iaa-gamelog.exe
   ```

2. **插入测试数据**
   - 根据你使用的数据库类型，执行上述相应的 SQL 或 MongoDB 命令

3. **运行基础测试**
   ```bash
   cd test
   go run test-utils.go
   ```

4. **运行完整验证码测试**
   ```bash
   cd test
   go run test-logincode-new.go
   ```

## 测试用例说明

### 测试用例 1: 首次验证有效验证码
- **请求**: `GET /check/login-code?app_name=test_app&code=valid_code_123`
- **期望**: 返回成功，包含生成的 ca_code
- **验证**: 数据库中的记录应该被更新，ca_code 字段填入生成的 MD5 值

### 测试用例 2: 验证已使用的验证码
- **请求**: `GET /check/login-code?app_name=test_app&code=used_code_456`
- **期望**: 返回失败，消息为"验证码已使用"

### 测试用例 3: 验证不存在的验证码
- **请求**: `GET /check/login-code?app_name=test_app&code=nonexistent_code`
- **期望**: 返回失败，消息为"验证码无效"

### 测试用例 4: ca_code 匹配验证
- **请求**: `GET /check/login-code?app_name=test_app&code=used_code_456&ca_code=5d41402abc4b2a76b9719d911017c592`
- **期望**: 返回成功，验证通过

### 测试用例 5: ca_code 不匹配验证
- **请求**: `GET /check/login-code?app_name=test_app&code=used_code_456&ca_code=invalid_ca_code`
- **期望**: 返回失败，消息为"验证码过期"

## 清理测试数据

测试完成后，可以清理测试数据：

### MongoDB
```javascript
db.ca_code_list.deleteMany({app_name: "test_app"});
db.ca_code_list.deleteMany({app_name: "test_workflow"});
```

### SQL Server / SQLite
```sql
DELETE FROM ca_code_list WHERE app_name IN ('test_app', 'test_workflow');
```

## 注意事项

1. 确保数据库连接正常
2. 确保 ca_code_list 表/集合已经创建
3. 测试数据中的 MD5 值 `5d41402abc4b2a76b9719d911017c592` 是字符串 "hello" 的 MD5 值
4. 实际测试时，系统会生成基于 code 的 MD5 值作为 ca_code
