# IAA GameLog - BaseNet API 游戏服务

一个完全基于BaseNet API规范的高性能游戏服务，使用Go语言开发，支持SQLite、MongoDB和SQL Server三种数据库。

**核心功能：**
- 🎮 游戏配置管理（启动配置、分享配置、广告配置）
- 👤 多平台用户登录（微信、百度、QQ）
- 💾 安全的游戏数据存储（支持MD5签名验证）
- 📊 完整的统计上报（登录、游戏行为、广告、分享、提示统计）
- 🛠️ 实用工具接口（服务器时间、IP检查、登录码验证）
- 💬 微信生态支持（数据解密、分享卡片、分享信息）
- 📱 头条平台支持（内容审核、圈子礼品、奖励系统）
- 🔍 其他扩展功能（素材管理、广告信息、白名单检查）

## 功能特性

- 🚀 **高性能**: SQLite WAL模式优化，支持高并发读写
- 🗄️ **多数据库支持**: 支持SQLite、MongoDB和SQL Server，可灵活切换
- 📦 **轻量级**: 单个exe文件，无需额外依赖
- 🔧 **简单易用**: RESTful API设计，接口简洁明了
- 💾 **数据持久化**: 可靠的数据存储，支持事务处理
- 🌐 **跨域支持**: 内置CORS支持，便于前端集成
- ⚙️ **配置灵活**: YAML配置文件，支持多环境部署

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置数据库

项目使用YAML配置文件，默认配置文件为`config.yaml`：

```yaml
# 数据库配置
database:
  # 数据库类型: sqlite、mongodb 或 sqlserver
  type: sqlite

  # SQLite配置
  sqlite:
    path: ./game_records.db

  # MongoDB配置
  mongodb:
    uri: mongodb://localhost:27017
    database: gamelog
    collection: game_records

  # SQL Server配置
  sqlserver:
    server: localhost
    port: 1433
    database: gamelog
    username: sa
    password: your_password
    instance: ""  # 实例名（可选）
    encrypt: true  # 是否启用加密

# 服务器配置
server:
  port: 8080
  host: localhost
  
  # CORS配置
  cors:
    enabled: true
    allowed_origins:
      - "*"
    allowed_methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed_headers:
      - "*"

# 日志配置
logging:
  level: info
  format: text
  output: stdout

# 安全配置
security:
  app_secret: "your_app_secret_key_here"

# BaseNet API配置
basenet:
  platforms:
    wechat:
      app_id: "your_wechat_app_id"
      app_secret: "your_wechat_app_secret"
  game_config:
    max_level: 100
    daily_reward: true
  ads_config:
    banner:
      enabled: true
```

#### 数据库支持说明

项目支持三种数据库类型：

**SQLite（默认）**
- 适合开发和小规模部署
- 无需额外安装数据库服务
- 自动创建数据库文件

**MongoDB**
- 适合大规模部署和高并发场景
- 需要安装MongoDB服务
- 支持分布式部署

**SQL Server**
- 支持 SQL Server 2016/2017/2019/2022
- 适合企业级部署和高并发场景
- 支持高可用和集群部署
- 配置参数说明：
  - `server`: 服务器地址
  - `port`: 端口号（默认1433）
  - `database`: 数据库名
  - `username`: 用户名
  - `password`: 密码
  - `instance`: 实例名（可选，用于命名实例）
  - `encrypt`: 是否启用加密连接（生产环境建议true）

### 3. 启动服务

```bash
# 编译
go build -o gamelog.exe .

# 运行
.\gamelog.exe
```

服务将在 `http://localhost:3001` 启动（端口可在config.yaml中配置）

## API 接口

### 核心API接口

**总计18个API接口，基于BaseNet API规范**

#### 游戏配置相关
- `GET /common/config/info` - 获取游戏启动配置
- `GET /common/config/share_list` - 获取分享配置

#### 数据存储相关
- `POST /common/game-data/s-save` - 保存游戏数据（需要签名验证）
- `GET /common/game-data/get` - 获取游戏数据
- `GET /common/game-data/multi-get` - 批量获取游戏数据

#### 统计上报相关
- `POST /statistics/login_log` - 登录日志统计
- `POST /statistics/game` - 游戏行为统计
- `POST /statistics/ad/show` - 广告展示统计
- `POST /statistics/ad/hit` - 广告点击统计
- `POST /statistics/share/show` - 分享展示统计
- `POST /statistics/hint` - 提示统计

#### 工具类接口
- `GET /check/time` - 获取服务器时间
- `GET /check/ip/is` - IP检查
- `GET /check/login-code` - 登录码验证
- `GET /check/uuidInWhitelist` - UUID白名单检查（需要签名验证）

#### 白名单管理接口
- `POST /whitelist/add` - 添加到白名单（需要签名验证）
- `DELETE /whitelist/remove` - 从白名单移除（需要签名验证）
- `GET /whitelist/list` - 获取白名单记录（需要签名验证）
- `GET /whitelist/check` - 检查白名单状态（需要签名验证）

#### API功能特性

**🔐 安全性**
- MD5签名验证机制，确保数据传输安全
- 登录码MD5加密处理
- 参数验证和错误处理

**🌐 平台支持**
- 微信小游戏生态完整支持
- 头条/抖音平台特色功能
- 多平台登录统一接口

**📊 数据统计**
- 完整的用户行为统计
- 广告展示和点击追踪
- 分享行为数据收集
- 提示和引导效果统计

**🎮 游戏功能**
- 圈子礼品系统
- 内容审核机制
- 素材管理接口
- 白名单权限控制

#### 签名验证机制

部分API（如`/common/game-data/s-save`）需要MD5签名验证：

**签名参数：**
- `timestamp`: 客户端时间戳（秒）
- `nonce`: 随机字符串
- `sign`: MD5签名

**签名算法：**
1. 将所有参数（除sign外）按字母排序
2. 拼接为 `key1=value1&key2=value2` 格式
3. 在末尾添加AppSecret
4. 计算MD5值作为签名

**示例：**
```javascript
// 参数
const params = {
  app_name: "test_app",
  uuid: "user123",
  d_key: "save_data",
  d_data: "{}",
  timestamp: **********,
  nonce: "abc123"
};

// 排序并拼接
const paramStr = "app_name=test_app&d_data={}&d_key=save_data&nonce=abc123&timestamp=**********&uuid=user123";

// 添加密钥并计算MD5
const sign = md5(paramStr + "your_app_secret_key_here");
```

### 系统接口

#### 健康检查

**GET** `/health`

**响应：**
```json
{
  "status": "ok"
}
```

## API 使用示例

### 1. 获取游戏配置

```bash
curl "http://localhost:3001/common/config/info?app_name=my_game&version=1.0.0"
```

### 2. 保存游戏数据（需要签名）

```bash
curl -X POST "http://localhost:3001/common/game-data/s-save" \
  -H "Content-Type: application/json" \
  -d '{
    "app_name": "my_game",
    "version": "1.0.0",
    "uuid": "user123",
    "d_key": "player_data",
    "d_data": "{\"level\":5,\"score\":1000}",
    "timestamp": **********,
    "nonce": "abc123",
    "sign": "calculated_md5_signature"
  }'
```

### 3. 获取游戏数据

```bash
curl "http://localhost:3001/common/game-data/get?app_name=my_game&version=1.0.0&uuid=user123&d_key=player_data"
```

### 4. UUID白名单检查

```bash
curl "http://localhost:3001/check/uuidInWhitelist?uuid=test_user_001&app_name=my_game&signature=your_signature"
```

### 5. 统计上报

```bash
curl -X POST "http://localhost:3001/statistics/login_log" \
  -H "Content-Type: application/json" \
  -d '{
    "app_name": "my_game",
    "channel": "wechat",
    "version": "1.0.0",
    "uuid": "user123",
    "platform": "wechat_game"
  }'
```

### 6. 获取服务器时间

```bash
curl "http://localhost:3001/check/time"
```

## 测试验证

项目提供了测试工具用于验证各个功能模块：

```bash
# 运行所有功能测试
go run test/test-all.go

# 测试特定模块
go run test/test-save.go    # 数据存储测试
go run test/test-config.go  # 配置接口测试
go run test/test-login.go   # 登录接口测试
```



## 数据库支持

### SQLite（默认）

**优势：**
- 无需额外安装，开箱即用
- 高性能，适合中小型应用
- WAL模式支持并发读写
- 数据文件便于备份和迁移

**优化配置：**
- WAL模式：支持并发读写
- 连接池：优化并发性能
- 内存映射：提升访问速度
- 智能重试：处理并发冲突

### MongoDB

**优势：**
- 原生支持JSON文档
- 水平扩展能力强
- 适合复杂数据结构
- 高可用性支持

**使用MongoDB：**

1. 启动MongoDB服务：
```bash
# 使用Docker
docker run -d --name mongodb -p 27017:27017 mongo:latest
```

2. 修改配置文件：
```yaml
database:
  type: mongodb
  mongodb:
    uri: mongodb://localhost:27017
    database: gamelog
    collection: game_records
```

## 性能优化

### SQLite优化

- **WAL模式**: 支持多读者并发访问
- **连接池**: 10个最大连接，5个空闲连接
- **内存映射**: 256MB mmap提升访问速度
- **缓存优化**: 4000页缓存大小
- **重试机制**: 智能处理并发冲突

### 应用层优化

- **预编译SQL**: 提升查询性能
- **事务处理**: 保证数据一致性
- **分页查询**: 支持大数据量查询
- **CORS优化**: 减少预检请求

## 使用示例

## 客户端对接指南

### JavaScript客户端示例

```javascript
// BaseNet API 客户端对接示例
class GameClient {
  constructor(baseUrl, appName, appSecret) {
    this.baseUrl = baseUrl;
    this.appName = appName;
    this.appSecret = appSecret;
  }

  // 计算签名
  calculateSign(params) {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonce = Math.random().toString(36).substring(2);

    params.timestamp = timestamp;
    params.nonce = nonce;

    // 参数排序并拼接
    const sortedKeys = Object.keys(params).sort();
    const paramStr = sortedKeys.map(key => `${key}=${params[key]}`).join('&');

    // 计算MD5签名（需要引入MD5库）
    params.sign = md5(paramStr + this.appSecret);
    return params;
  }

  // 保存游戏数据
  async saveGameData(uuid, dataKey, gameData) {
    const params = {
      app_name: this.appName,
      version: "1.0.0",
      uuid: uuid,
      d_key: dataKey,
      d_data: JSON.stringify(gameData)
    };

    this.calculateSign(params);

    const response = await fetch(`${this.baseUrl}/common/game-data/s-save`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    });

    return response.json();
  }

  // 获取游戏数据
  async getGameData(uuid, dataKey) {
    const url = `${this.baseUrl}/common/game-data/get?app_name=${this.appName}&version=1.0.0&uuid=${uuid}&d_key=${dataKey}`;
    const response = await fetch(url);
    return response.json();
  }

  // 检查白名单
  async checkWhitelist(uuid, signature) {
    const url = `${this.baseUrl}/check/uuidInWhitelist?uuid=${uuid}&app_name=${this.appName}&signature=${signature}`;
    const response = await fetch(url);
    return response.json();
  }
}

// 使用示例
const client = new GameClient('http://localhost:3001', 'my_game', 'your_app_secret');
await client.saveGameData('player123', 'save_data', { level: 5, score: 1000 });
```

## 部署指南

### 本地部署

1. **编译项目**
   ```bash
   go build -o iaa-gamelog.exe .
   ```

2. **配置数据库**
   - 修改 `config.yaml` 中的数据库配置
   - 根据实际环境选择数据库类型（sqlite/mongodb/sqlserver）

3. **启动服务**
   ```bash
   ./iaa-gamelog.exe
   ```

4. **验证部署**
   ```bash
   curl http://localhost:3001/health
   ```

### 生产环境部署

1. **配置HTTPS**（推荐使用Nginx反向代理）
2. **配置数据库连接池**
3. **设置日志级别为 `warn` 或 `error`**
4. **配置CORS策略**（限制允许的域名）
5. **定期备份数据库**

### Windows服务部署

可以使用 NSSM 将程序注册为Windows服务：

```bash
# 下载NSSM并安装为服务
nssm install IAA-GameLog "C:\path\to\iaa-gamelog.exe"
nssm set IAA-GameLog AppDirectory "C:\path\to\project"
nssm start IAA-GameLog
```

## 系统架构

### 技术栈

- **语言**: Go 1.21+
- **Web框架**: Gin - 高性能HTTP框架
- **数据库**: SQLite/MongoDB/SQL Server - 三种数据库支持
- **配置管理**: Viper - YAML配置解析
- **日志系统**: 结构化日志输出
- **数据库驱动**: go-sqlite3 / mongo-driver / go-mssqldb

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   游戏客户端     │    │   BaseNet API   │    │   数据存储层     │
│                │    │                │    │                │
│  - 微信小游戏    │───▶│  - 25个API接口   │───▶│  - SQLite      │
│  - 头条小游戏    │    │  - 签名验证      │    │  - MongoDB     │
│  - QQ小游戏     │    │  - CORS支持     │    │  - SQL Server  │
│  - 百度小游戏    │    │  - 错误处理      │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心模块

1. **路由层** (`internal/router`) - API路由管理
2. **处理层** (`internal/handlers`) - 业务逻辑处理
3. **中间件** (`internal/middleware`) - 签名验证、CORS等
4. **数据层** (`internal/database`) - 数据库抽象接口
5. **模型层** (`internal/models`) - 数据结构定义
6. **配置层** (`internal/config`) - 配置管理

### 设计原则

- **接口抽象**: 数据库操作统一接口，支持多种数据库
- **BaseNet兼容**: 完全兼容BaseNet API规范
- **高性能**: 针对游戏场景的高并发优化
- **易部署**: 单文件部署，最小化依赖

## 重要说明

### 安全配置
- 生产环境请修改 `app_secret` 为强密码
- 配置适当的CORS策略，避免使用 `*` 通配符
- 建议使用HTTPS部署

### 数据库选择
- **SQLite**: 适合中小型应用，单机部署
- **MongoDB**: 适合大规模应用，支持分布式
- **SQL Server**: 适合企业级应用，高可用部署

### 性能优化
- SQLite使用WAL模式，支持并发读写
- 配置合适的连接池大小
- 定期清理过期数据和日志

### 备份策略
- SQLite: 定期备份 `.db` 文件
- MongoDB: 配置副本集或定期导出
- SQL Server: 配置自动备份计划

## 相关文档

- [BaseNet API详细文档](docs/BaseNet_API_Documentation.md) - 完整的API接口说明
- [配置文件](config.yaml) - 当前配置文件