package database

import (
	"iaa-gamelog/internal/models"
)

// MongoAdapter MongoDB适配器，实现DatabaseInterface接口
type MongoAdapter struct {
	mongoDB *MongoDatabase
}

// NewMongoAdapter 创建新的MongoDB适配器
func NewMongoAdapter(uri, dbName, collectionName string) (*MongoAdapter, error) {
	mongoDB, err := NewMongoDatabase(uri, dbName, collectionName)
	if err != nil {
		return nil, err
	}
	return &MongoAdapter{mongoDB: mongoDB}, nil
}

// Close 关闭MongoDB连接
func (m *MongoAdapter) Close() error {
	return m.mongoDB.Close()
}

// SaveRecord 保存游戏记录（不存在时新插入，存在时更新）
func (m *MongoAdapter) SaveRecord(userID, gameName string, gameData interface{}) (interface{}, string, error) {
	record, action, err := m.mongoDB.SaveRecord(userID, gameName, gameData)
	if err != nil {
		return nil, "", err
	}
	return record.ID.Hex(), action, nil
}

// GetRecords 获取游戏记录
func (m *MongoAdapter) GetRecords(userID, gameName string, limit, offset int) (interface{}, int64, error) {
	return m.mongoDB.GetRecords(userID, gameName, limit, offset)
}

// DeleteRecord 删除记录（实现DatabaseInterface接口）
func (m *MongoAdapter) DeleteRecord(userID, gameName string) error {
	return m.mongoDB.DeleteRecord(userID, gameName)
}

// GetRecordByID 根据ID获取记录（额外方法）
func (m *MongoAdapter) GetRecordByID(id string) (*MongoGameRecord, error) {
	return m.mongoDB.GetRecordByID(id)
}

// ==================== 白名单相关方法 ====================

// CheckWhitelist 检查UUID是否在白名单中
func (m *MongoAdapter) CheckWhitelist(appName, uuid string) (bool, error) {
	return m.mongoDB.CheckWhitelist(appName, uuid)
}

// AddToWhitelist 添加UUID到白名单
func (m *MongoAdapter) AddToWhitelist(appName, uuid string) error {
	return m.mongoDB.AddToWhitelist(appName, uuid)
}

// RemoveFromWhitelist 从白名单中移除UUID
func (m *MongoAdapter) RemoveFromWhitelist(appName, uuid string) error {
	return m.mongoDB.RemoveFromWhitelist(appName, uuid)
}

// GetWhitelistRecords 获取白名单记录
func (m *MongoAdapter) GetWhitelistRecords(appName string, limit, offset int) (interface{}, int64, error) {
	return m.mongoDB.GetWhitelistRecords(appName, limit, offset)
}

// ==================== 验证码相关方法 ====================

// CheckLoginCode 检查验证码
func (m *MongoAdapter) CheckLoginCode(appName, code, caCode string) (*models.LoginCodeRecord, error) {
	return m.mongoDB.CheckLoginCode(appName, code, caCode)
}

// UpdateLoginCodeCACode 更新验证码的 ca_code
func (m *MongoAdapter) UpdateLoginCodeCACode(appName, code, caCode string) error {
	return m.mongoDB.UpdateLoginCodeCACode(appName, code, caCode)
}
