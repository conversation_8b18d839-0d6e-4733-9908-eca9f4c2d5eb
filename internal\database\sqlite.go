package database

import (
	"database/sql"
	"encoding/json"
	"log"
	"strings"
	"time"

	"iaa-gamelog/internal/models"

	_ "modernc.org/sqlite"
)

// SQLiteDatabase SQLite数据库操作结构体
type SQLiteDatabase struct {
	db *sql.DB
}

// NewSQLiteDatabase 创建新的SQLite数据库连接
func NewSQLiteDatabase(dbPath string) (*SQLiteDatabase, error) {
	// 优化SQLite连接参数，充分利用WAL模式的并发能力
	db, err := sql.Open("sqlite", dbPath+"?cache=shared&mode=rwc&_journal_mode=WAL&_synchronous=NORMAL&_cache_size=4000&_temp_store=memory&_busy_timeout=30000&_wal_autocheckpoint=1000")
	if err != nil {
		return nil, err
	}

	// WAL模式支持多读者+单写者，调整连接池以提升并发性能
	db.SetMaxOpenConns(10) // WAL模式下可以支持更多并发连接
	db.SetMaxIdleConns(5)  // 保持一定数量的空闲连接
	db.SetConnMaxLifetime(30 * time.Minute)
	db.SetConnMaxIdleTime(5 * time.Minute) // 设置空闲连接超时

	// 确保WAL模式正确启用并优化设置
	_, err = db.Exec("PRAGMA journal_mode=WAL;")
	if err != nil {
		log.Printf("设置WAL模式失败: %v", err)
	}

	// 验证WAL模式是否启用
	var journalMode string
	err = db.QueryRow("PRAGMA journal_mode;").Scan(&journalMode)
	if err == nil {
		log.Printf("当前journal模式: %s", journalMode)
	}

	// 优化WAL模式性能设置
	db.Exec("PRAGMA synchronous=NORMAL;")      // 平衡性能和安全性
	db.Exec("PRAGMA cache_size=4000;")         // 增加缓存
	db.Exec("PRAGMA temp_store=memory;")       // 临时表存储在内存
	db.Exec("PRAGMA mmap_size=268435456;")     // 启用内存映射(256MB)
	db.Exec("PRAGMA wal_autocheckpoint=1000;") // WAL自动检查点

	// 创建表
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS game_records (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		user_id TEXT NOT NULL,
		game_name TEXT NOT NULL,
		game_data TEXT NOT NULL CHECK(json_valid(game_data)),
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(user_id, game_name)
	);

	CREATE INDEX IF NOT EXISTS idx_user_id ON game_records(user_id);
	CREATE INDEX IF NOT EXISTS idx_game_name ON game_records(game_name);
	CREATE INDEX IF NOT EXISTS idx_created_at ON game_records(created_at);
	CREATE INDEX IF NOT EXISTS idx_updated_at ON game_records(updated_at);
	`

	// 创建白名单表
	createWhitelistTableSQL := `
	CREATE TABLE IF NOT EXISTS whitelist (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		app_name TEXT NOT NULL,
		uuid TEXT NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(app_name, uuid)
	);

	CREATE INDEX IF NOT EXISTS idx_whitelist_app_name ON whitelist(app_name);
	CREATE INDEX IF NOT EXISTS idx_whitelist_uuid ON whitelist(uuid);
	CREATE INDEX IF NOT EXISTS idx_whitelist_created_at ON whitelist(created_at);
	`

	// 创建验证码表
	createCACodeTableSQL := `
	CREATE TABLE IF NOT EXISTS ca_code_list (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		app_name TEXT NOT NULL,
		code TEXT NOT NULL,
		ca_code TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(app_name, code)
	);

	CREATE INDEX IF NOT EXISTS idx_ca_code_app_name ON ca_code_list(app_name);
	CREATE INDEX IF NOT EXISTS idx_ca_code_code ON ca_code_list(code);
	CREATE INDEX IF NOT EXISTS idx_ca_code_ca_code ON ca_code_list(ca_code);
	CREATE INDEX IF NOT EXISTS idx_ca_code_created_at ON ca_code_list(created_at);
	`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		// 如果表已存在但结构不同，尝试更新表结构
		log.Println("表可能已存在，尝试更新表结构...")

		// 分别执行每个ALTER语句，忽略已存在列的错误
		db.Exec("ALTER TABLE game_records ADD COLUMN game_data TEXT CHECK(json_valid(game_data))")
		db.Exec("ALTER TABLE game_records ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP")

		// 创建索引
		db.Exec("CREATE INDEX IF NOT EXISTS idx_updated_at ON game_records(updated_at)")

		log.Println("表结构更新完成")
	}

	// 创建白名单表
	_, err = db.Exec(createWhitelistTableSQL)
	if err != nil {
		log.Printf("创建白名单表失败: %v", err)
	} else {
		log.Println("白名单表创建成功")
	}

	// 创建验证码表
	_, err = db.Exec(createCACodeTableSQL)
	if err != nil {
		log.Printf("创建验证码表失败: %v", err)
	} else {
		log.Println("验证码表创建成功")
	}

	log.Println("SQLite数据库初始化成功，WAL模式已启用")

	return &SQLiteDatabase{db: db}, nil
}

// Close 关闭SQLite数据库连接
func (s *SQLiteDatabase) Close() error {
	return s.db.Close()
}

// SaveRecord 保存游戏记录（不存在时新插入，存在时更新）
func (s *SQLiteDatabase) SaveRecord(userID, gameName string, gameData interface{}) (interface{}, string, error) {
	// 将游戏数据转换为JSON字符串
	gameDataJSON, err := json.Marshal(gameData)
	if err != nil {
		return nil, "", err
	}

	var action string
	var recordId int64

	// 使用重试机制和事务处理并发问题
	err = retryOnBusy(func() error {
		// 开始事务
		tx, err := s.db.Begin()
		if err != nil {
			return err
		}
		defer tx.Rollback() // 如果没有提交，自动回滚

		now := time.Now()

		// 使用UPSERT语句（INSERT OR REPLACE）
		stmt, err := tx.Prepare(`
			INSERT INTO game_records (user_id, game_name, game_data, created_at, updated_at) 
			VALUES (?, ?, ?, ?, ?)
			ON CONFLICT(user_id, game_name) 
			DO UPDATE SET 
				game_data = excluded.game_data,
				updated_at = excluded.updated_at
		`)
		if err != nil {
			return err
		}
		defer stmt.Close()

		result, err := stmt.Exec(userID, gameName, string(gameDataJSON), now, now)
		if err != nil {
			return err
		}

		// 获取插入ID来判断是插入还是更新
		lastInsertId, _ := result.LastInsertId()

		if lastInsertId > 0 {
			action = "created"
			recordId = lastInsertId
		} else {
			action = "updated"
			// 获取更新记录的ID
			err = tx.QueryRow("SELECT id FROM game_records WHERE user_id = ? AND game_name = ?", userID, gameName).Scan(&recordId)
			if err != nil {
				return err
			}
		}

		// 提交事务
		return tx.Commit()
	}, 5) // 最多重试5次

	if err != nil {
		return nil, "", err
	}

	return recordId, action, nil
}

// GetRecords 获取游戏记录
func (s *SQLiteDatabase) GetRecords(userID, gameName string, limit, offset int) (interface{}, int64, error) {
	// 构建查询条件
	var whereClause string
	var args []interface{}
	var countArgs []interface{}

	if userID != "" && gameName != "" {
		whereClause = "WHERE user_id = ? AND game_name = ?"
		args = append(args, userID, gameName)
		countArgs = append(countArgs, userID, gameName)
	} else if userID != "" {
		whereClause = "WHERE user_id = ?"
		args = append(args, userID)
		countArgs = append(countArgs, userID)
	} else if gameName != "" {
		whereClause = "WHERE game_name = ?"
		args = append(args, gameName)
		countArgs = append(countArgs, gameName)
	}

	var total int64
	var records []models.GameRecord

	// WAL模式下读操作很少冲突，简化重试逻辑
	err := retryOnBusy(func() error {
		// 获取总数
		countSQL := "SELECT COUNT(*) FROM game_records " + whereClause
		err := s.db.QueryRow(countSQL, countArgs...).Scan(&total)
		if err != nil {
			return err
		}

		// 获取记录
		querySQL := "SELECT id, user_id, game_name, game_data, created_at, updated_at FROM game_records " + whereClause + " ORDER BY updated_at DESC LIMIT ? OFFSET ?"
		queryArgs := append(args, limit, offset)

		rows, err := s.db.Query(querySQL, queryArgs...)
		if err != nil {
			return err
		}
		defer rows.Close()

		records = nil // 重置records切片
		for rows.Next() {
			var record models.GameRecord
			err := rows.Scan(&record.ID, &record.UserID, &record.GameName, &record.GameData, &record.CreatedAt, &record.UpdatedAt)
			if err != nil {
				return err
			}
			records = append(records, record)
		}
		return nil
	}, 2) // WAL模式下读操作减少重试次数

	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// DeleteRecord 删除游戏记录
func (s *SQLiteDatabase) DeleteRecord(userID, gameName string) error {
	// 使用重试机制处理并发问题
	err := retryOnBusy(func() error {
		// 开始事务
		tx, err := s.db.Begin()
		if err != nil {
			return err
		}
		defer tx.Rollback() // 如果没有提交，自动回滚

		// 删除记录
		stmt, err := tx.Prepare("DELETE FROM game_records WHERE user_id = ? AND game_name = ?")
		if err != nil {
			return err
		}
		defer stmt.Close()

		result, err := stmt.Exec(userID, gameName)
		if err != nil {
			return err
		}

		// 检查是否有记录被删除
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}

		if rowsAffected == 0 {
			return sql.ErrNoRows
		}

		// 提交事务
		return tx.Commit()
	}, 3) // 删除操作重试3次

	return err
}

// ==================== 白名单相关方法 ====================

// CheckWhitelist 检查UUID是否在白名单中
func (s *SQLiteDatabase) CheckWhitelist(appName, uuid string) (bool, error) {
	var exists bool
	err := retryOnBusy(func() error {
		query := "SELECT EXISTS(SELECT 1 FROM whitelist WHERE app_name = ? AND uuid = ?)"
		return s.db.QueryRow(query, appName, uuid).Scan(&exists)
	}, 3)

	if err != nil {
		return false, err
	}
	return exists, nil
}

// AddToWhitelist 添加UUID到白名单
func (s *SQLiteDatabase) AddToWhitelist(appName, uuid string) error {
	return retryOnBusy(func() error {
		tx, err := s.db.Begin()
		if err != nil {
			return err
		}
		defer tx.Rollback()

		now := time.Now()
		stmt, err := tx.Prepare(`
			INSERT INTO whitelist (app_name, uuid, created_at, updated_at)
			VALUES (?, ?, ?, ?)
			ON CONFLICT(app_name, uuid)
			DO UPDATE SET updated_at = excluded.updated_at
		`)
		if err != nil {
			return err
		}
		defer stmt.Close()

		_, err = stmt.Exec(appName, uuid, now, now)
		if err != nil {
			return err
		}

		return tx.Commit()
	}, 3)
}

// RemoveFromWhitelist 从白名单中移除UUID
func (s *SQLiteDatabase) RemoveFromWhitelist(appName, uuid string) error {
	return retryOnBusy(func() error {
		tx, err := s.db.Begin()
		if err != nil {
			return err
		}
		defer tx.Rollback()

		stmt, err := tx.Prepare("DELETE FROM whitelist WHERE app_name = ? AND uuid = ?")
		if err != nil {
			return err
		}
		defer stmt.Close()

		result, err := stmt.Exec(appName, uuid)
		if err != nil {
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}

		if rowsAffected == 0 {
			return sql.ErrNoRows
		}

		return tx.Commit()
	}, 3)
}

// GetWhitelistRecords 获取白名单记录
func (s *SQLiteDatabase) GetWhitelistRecords(appName string, limit, offset int) (interface{}, int64, error) {
	var records []models.Whitelist
	var total int64

	err := retryOnBusy(func() error {
		// 构建查询条件
		whereClause := ""
		args := []interface{}{}

		if appName != "" {
			whereClause = "WHERE app_name = ?"
			args = append(args, appName)
		}

		// 获取总数
		countSQL := "SELECT COUNT(*) FROM whitelist " + whereClause
		err := s.db.QueryRow(countSQL, args...).Scan(&total)
		if err != nil {
			return err
		}

		// 获取记录
		querySQL := "SELECT id, app_name, uuid, created_at, updated_at FROM whitelist " + whereClause + " ORDER BY created_at DESC LIMIT ? OFFSET ?"
		queryArgs := append(args, limit, offset)

		rows, err := s.db.Query(querySQL, queryArgs...)
		if err != nil {
			return err
		}
		defer rows.Close()

		records = []models.Whitelist{}
		for rows.Next() {
			var record models.Whitelist
			err := rows.Scan(&record.ID, &record.AppName, &record.UUID, &record.CreatedAt, &record.UpdatedAt)
			if err != nil {
				return err
			}
			records = append(records, record)
		}

		return rows.Err()
	}, 3)

	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// 重试机制：处理SQLite BUSY错误
func retryOnBusy(operation func() error, maxRetries int) error {
	var err error
	for i := 0; i <= maxRetries; i++ {
		err = operation()
		if err == nil {
			return nil
		}

		// 检查是否是SQLITE_BUSY错误
		if strings.Contains(err.Error(), "database is locked") || strings.Contains(err.Error(), "SQLITE_BUSY") {
			if i < maxRetries {
				// 指数退避：等待时间逐渐增加
				waitTime := time.Duration(10*(i+1)) * time.Millisecond
				time.Sleep(waitTime)
				continue
			}
		}

		// 非BUSY错误或重试次数用完，直接返回
		return err
	}
	return err
}
